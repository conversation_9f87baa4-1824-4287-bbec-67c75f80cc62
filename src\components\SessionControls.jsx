import { useState, useEffect } from 'react'

function SessionControls({ isSessionActive, setIsSessionActive, sessionType, blockedSites }) {
  const [blockedAttempts, setBlockedAttempts] = useState([])

  // Simulate blocking attempts (in a real app, this would come from browser extension)
  useEffect(() => {
    if (isSessionActive && sessionType === 'work') {
      // Simulate some blocked attempts for demo purposes
      const interval = setInterval(() => {
        if (Math.random() > 0.8) { // 20% chance every 10 seconds
          const randomSite = blockedSites[Math.floor(Math.random() * blockedSites.length)]
          if (randomSite) {
            const newAttempt = {
              site: randomSite,
              timestamp: new Date(),
              id: Date.now()
            }
            setBlockedAttempts(prev => [newAttempt, ...prev.slice(0, 4)]) // Keep last 5
          }
        }
      }, 10000)

      return () => clearInterval(interval)
    }
  }, [isSessionActive, sessionType, blockedSites])

  const emergencyStop = () => {
    setIsSessionActive(false)
    setBlockedAttempts([])
  }

  const formatTime = (date) => {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
  }

  return (
    <div className="space-y-4">
      {/* Session Status Card */}
      <div className={`card ${isSessionActive ? 'border-l-4' : ''} ${
        isSessionActive && sessionType === 'work' 
          ? 'border-l-red-500 bg-red-50' 
          : isSessionActive && sessionType === 'break'
          ? 'border-l-green-500 bg-green-50'
          : 'bg-white'
      }`}>
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-gray-900">
              {isSessionActive 
                ? (sessionType === 'work' ? '🔒 Focus Mode Active' : '🌱 Break Time')
                : '⏸️ Session Paused'
              }
            </h3>
            <p className="text-gray-600 text-sm">
              {isSessionActive && sessionType === 'work' 
                ? `${blockedSites.length} websites are currently blocked`
                : isSessionActive && sessionType === 'break'
                ? 'All websites are accessible during break time'
                : 'Start a timer to begin blocking distracting websites'
              }
            </p>
          </div>
          
          {isSessionActive && (
            <button
              onClick={emergencyStop}
              className="btn-danger text-sm"
            >
              🚨 Emergency Stop
            </button>
          )}
        </div>
      </div>

      {/* Blocked Sites Status */}
      {isSessionActive && sessionType === 'work' && blockedSites.length > 0 && (
        <div className="card">
          <h4 className="font-semibold text-gray-900 mb-3">🚫 Currently Blocked Sites</h4>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
            {blockedSites.slice(0, 6).map((site, index) => (
              <div
                key={index}
                className="flex items-center space-x-2 p-2 bg-red-50 rounded-lg border border-red-200"
              >
                <span className="text-red-500 text-sm">🚫</span>
                <span className="text-red-700 text-sm font-medium truncate">{site}</span>
              </div>
            ))}
            {blockedSites.length > 6 && (
              <div className="flex items-center justify-center p-2 bg-gray-50 rounded-lg border border-gray-200">
                <span className="text-gray-500 text-sm">+{blockedSites.length - 6} more</span>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Recent Blocked Attempts */}
      {blockedAttempts.length > 0 && (
        <div className="card">
          <h4 className="font-semibold text-gray-900 mb-3">⚠️ Recent Blocked Attempts</h4>
          <div className="space-y-2">
            {blockedAttempts.map((attempt) => (
              <div
                key={attempt.id}
                className="flex items-center justify-between p-3 bg-yellow-50 rounded-lg border border-yellow-200"
              >
                <div className="flex items-center space-x-3">
                  <span className="text-yellow-600">⚠️</span>
                  <div>
                    <span className="font-medium text-yellow-900">{attempt.site}</span>
                    <p className="text-yellow-700 text-xs">Blocked access attempt</p>
                  </div>
                </div>
                <span className="text-yellow-600 text-sm">
                  {formatTime(attempt.timestamp)}
                </span>
              </div>
            ))}
          </div>
          <p className="text-gray-500 text-xs mt-3">
            💡 Stay focused! These distractions were automatically blocked.
          </p>
        </div>
      )}

      {/* Focus Tips */}
      {isSessionActive && sessionType === 'work' && (
        <div className="card bg-blue-50 border-blue-200">
          <h4 className="font-semibold text-blue-900 mb-3">💡 Focus Tips</h4>
          <div className="space-y-2 text-blue-800 text-sm">
            <p>• Keep your phone in another room or in airplane mode</p>
            <p>• Close unnecessary browser tabs and applications</p>
            <p>• Use noise-canceling headphones or focus music</p>
            <p>• Take deep breaths if you feel the urge to check social media</p>
          </div>
        </div>
      )}

      {/* Break Suggestions */}
      {isSessionActive && sessionType === 'break' && (
        <div className="card bg-green-50 border-green-200">
          <h4 className="font-semibold text-green-900 mb-3">🌱 Break Suggestions</h4>
          <div className="space-y-2 text-green-800 text-sm">
            <p>• Stand up and stretch your body</p>
            <p>• Look away from your screen (20-20-20 rule)</p>
            <p>• Take a short walk or get some fresh air</p>
            <p>• Hydrate with water or herbal tea</p>
            <p>• Practice deep breathing or meditation</p>
          </div>
        </div>
      )}
    </div>
  )
}

export default SessionControls
