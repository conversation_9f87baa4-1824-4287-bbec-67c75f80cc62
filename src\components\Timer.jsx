import { useState, useEffect, useRef } from 'react'

function Timer({ settings, isSessionActive, setIsSessionActive, sessionType, setSessionType, blockedSites }) {
  const [timeLeft, setTimeLeft] = useState(settings.workDuration * 60)
  const [completedSessions, setCompletedSessions] = useState(0)
  const intervalRef = useRef(null)

  // Update timer when settings change
  useEffect(() => {
    if (!isSessionActive) {
      const duration = sessionType === 'work' 
        ? settings.workDuration 
        : (completedSessions % settings.sessionsUntilLongBreak === 0 && completedSessions > 0)
          ? settings.longBreakDuration
          : settings.shortBreakDuration
      setTimeLeft(duration * 60)
    }
  }, [settings, sessionType, completedSessions, isSessionActive])

  // Timer countdown logic
  useEffect(() => {
    if (isSessionActive && timeLeft > 0) {
      intervalRef.current = setInterval(() => {
        setTimeLeft(prev => {
          if (prev <= 1) {
            handleTimerComplete()
            return 0
          }
          return prev - 1
        })
      }, 1000)
    } else {
      clearInterval(intervalRef.current)
    }

    return () => clearInterval(intervalRef.current)
  }, [isSessionActive, timeLeft])

  const handleTimerComplete = () => {
    setIsSessionActive(false)
    
    if (sessionType === 'work') {
      const newCompletedSessions = completedSessions + 1
      setCompletedSessions(newCompletedSessions)
      
      // Save completed session to localStorage
      const today = new Date().toDateString()
      const stats = JSON.parse(localStorage.getItem('focuszone-stats') || '{}')
      stats[today] = (stats[today] || 0) + 1
      localStorage.setItem('focuszone-stats', JSON.stringify(stats))
      
      // Switch to break
      setSessionType('break')
      const isLongBreak = newCompletedSessions % settings.sessionsUntilLongBreak === 0
      setTimeLeft((isLongBreak ? settings.longBreakDuration : settings.shortBreakDuration) * 60)
    } else {
      // Switch back to work
      setSessionType('work')
      setTimeLeft(settings.workDuration * 60)
    }

    // Show notification
    if (settings.enableNotifications && 'Notification' in window) {
      if (Notification.permission === 'granted') {
        new Notification(`FocusZone - ${sessionType === 'work' ? 'Break Time!' : 'Back to Work!'}`, {
          body: sessionType === 'work' 
            ? 'Great job! Time for a break.' 
            : 'Break is over. Ready to focus?',
          icon: '/vite.svg'
        })
      }
    }

    // Play sound
    if (settings.enableSounds) {
      const audio = new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT')
      audio.play().catch(() => {}) // Ignore errors if audio fails
    }
  }

  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }

  const getProgress = () => {
    const totalDuration = sessionType === 'work' 
      ? settings.workDuration * 60
      : (completedSessions % settings.sessionsUntilLongBreak === 0 && completedSessions > 0)
        ? settings.longBreakDuration * 60
        : settings.shortBreakDuration * 60
    return ((totalDuration - timeLeft) / totalDuration) * 100
  }

  const startTimer = () => {
    // Request notification permission
    if (settings.enableNotifications && 'Notification' in window && Notification.permission === 'default') {
      Notification.requestPermission()
    }
    
    setIsSessionActive(true)
  }

  const pauseTimer = () => {
    setIsSessionActive(false)
  }

  const resetTimer = () => {
    setIsSessionActive(false)
    const duration = sessionType === 'work' 
      ? settings.workDuration 
      : (completedSessions % settings.sessionsUntilLongBreak === 0 && completedSessions > 0)
        ? settings.longBreakDuration
        : settings.shortBreakDuration
    setTimeLeft(duration * 60)
  }

  const skipSession = () => {
    setIsSessionActive(false)
    if (sessionType === 'work') {
      setSessionType('break')
      const isLongBreak = (completedSessions + 1) % settings.sessionsUntilLongBreak === 0
      setTimeLeft((isLongBreak ? settings.longBreakDuration : settings.shortBreakDuration) * 60)
    } else {
      setSessionType('work')
      setTimeLeft(settings.workDuration * 60)
    }
  }

  return (
    <div className="card max-w-md mx-auto text-center">
      <div className="mb-6">
        <h2 className="text-3xl font-bold text-gray-900 mb-2">
          {sessionType === 'work' ? '🎯 Focus Time' : '☕ Break Time'}
        </h2>
        <p className="text-gray-600">
          {sessionType === 'work' 
            ? 'Stay focused and avoid distractions' 
            : 'Take a break and recharge'}
        </p>
      </div>

      {/* Timer Display */}
      <div className="mb-8">
        <div className="relative w-48 h-48 mx-auto mb-4">
          <svg className="w-full h-full transform -rotate-90" viewBox="0 0 100 100">
            <circle
              cx="50"
              cy="50"
              r="45"
              stroke="currentColor"
              strokeWidth="2"
              fill="none"
              className="text-gray-200"
            />
            <circle
              cx="50"
              cy="50"
              r="45"
              stroke="currentColor"
              strokeWidth="2"
              fill="none"
              strokeDasharray={`${2 * Math.PI * 45}`}
              strokeDashoffset={`${2 * Math.PI * 45 * (1 - getProgress() / 100)}`}
              className={`transition-all duration-1000 ${
                sessionType === 'work' ? 'text-blue-600' : 'text-green-600'
              }`}
              strokeLinecap="round"
            />
          </svg>
          <div className="absolute inset-0 flex items-center justify-center">
            <span className="text-4xl font-mono font-bold text-gray-900">
              {formatTime(timeLeft)}
            </span>
          </div>
        </div>
      </div>

      {/* Controls */}
      <div className="flex justify-center space-x-3 mb-6">
        {!isSessionActive ? (
          <button
            onClick={startTimer}
            className="btn-primary px-6 py-3 text-lg"
          >
            ▶️ Start
          </button>
        ) : (
          <button
            onClick={pauseTimer}
            className="btn-secondary px-6 py-3 text-lg"
          >
            ⏸️ Pause
          </button>
        )}
        
        <button
          onClick={resetTimer}
          className="btn-secondary px-4 py-3"
        >
          🔄 Reset
        </button>
        
        <button
          onClick={skipSession}
          className="btn-secondary px-4 py-3"
        >
          ⏭️ Skip
        </button>
      </div>

      {/* Session Info */}
      <div className="text-sm text-gray-600 space-y-1">
        <p>Completed Sessions: {completedSessions}</p>
        <p>
          Next: {sessionType === 'work' 
            ? ((completedSessions + 1) % settings.sessionsUntilLongBreak === 0 ? 'Long Break' : 'Short Break')
            : 'Work Session'
          }
        </p>
      </div>
    </div>
  )
}

export default Timer
