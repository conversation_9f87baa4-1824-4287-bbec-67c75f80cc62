import { useState } from 'react'

function Settings({ settings, setSettings }) {
  const [tempSettings, setTempSettings] = useState(settings)
  const [hasChanges, setHasChanges] = useState(false)

  const handleChange = (key, value) => {
    setTempSettings(prev => ({
      ...prev,
      [key]: value
    }))
    setHasChanges(true)
  }

  const saveSettings = () => {
    setSettings(tempSettings)
    setHasChanges(false)
  }

  const resetSettings = () => {
    const defaultSettings = {
      workDuration: 25,
      shortBreakDuration: 5,
      longBreakDuration: 15,
      sessionsUntilLongBreak: 4,
      enableNotifications: true,
      enableSounds: true
    }
    setTempSettings(defaultSettings)
    setHasChanges(true)
  }

  const cancelChanges = () => {
    setTempSettings(settings)
    setHasChanges(false)
  }

  return (
    <div className="max-w-2xl mx-auto space-y-6">
      <div className="card">
        <h2 className="text-2xl font-bold text-gray-900 mb-6">⚙️ Settings</h2>

        {/* Timer Settings */}
        <div className="mb-8">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">⏱️ Timer Settings</h3>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Work Session Duration (minutes)
              </label>
              <input
                type="number"
                min="1"
                max="120"
                value={tempSettings.workDuration}
                onChange={(e) => handleChange('workDuration', parseInt(e.target.value) || 25)}
                className="input-field w-32"
              />
              <p className="text-xs text-gray-500 mt-1">Recommended: 25 minutes</p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Short Break Duration (minutes)
              </label>
              <input
                type="number"
                min="1"
                max="30"
                value={tempSettings.shortBreakDuration}
                onChange={(e) => handleChange('shortBreakDuration', parseInt(e.target.value) || 5)}
                className="input-field w-32"
              />
              <p className="text-xs text-gray-500 mt-1">Recommended: 5 minutes</p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Long Break Duration (minutes)
              </label>
              <input
                type="number"
                min="1"
                max="60"
                value={tempSettings.longBreakDuration}
                onChange={(e) => handleChange('longBreakDuration', parseInt(e.target.value) || 15)}
                className="input-field w-32"
              />
              <p className="text-xs text-gray-500 mt-1">Recommended: 15-30 minutes</p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Sessions Until Long Break
              </label>
              <input
                type="number"
                min="2"
                max="10"
                value={tempSettings.sessionsUntilLongBreak}
                onChange={(e) => handleChange('sessionsUntilLongBreak', parseInt(e.target.value) || 4)}
                className="input-field w-32"
              />
              <p className="text-xs text-gray-500 mt-1">How many work sessions before a long break</p>
            </div>
          </div>
        </div>

        {/* Notification Settings */}
        <div className="mb-8">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">🔔 Notifications</h3>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-gray-700">
                  Enable Notifications
                </label>
                <p className="text-xs text-gray-500">Get notified when sessions start/end</p>
              </div>
              <button
                onClick={() => handleChange('enableNotifications', !tempSettings.enableNotifications)}
                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                  tempSettings.enableNotifications ? 'bg-blue-600' : 'bg-gray-200'
                }`}
              >
                <span
                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                    tempSettings.enableNotifications ? 'translate-x-6' : 'translate-x-1'
                  }`}
                />
              </button>
            </div>

            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-gray-700">
                  Enable Sounds
                </label>
                <p className="text-xs text-gray-500">Play sound when timer completes</p>
              </div>
              <button
                onClick={() => handleChange('enableSounds', !tempSettings.enableSounds)}
                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                  tempSettings.enableSounds ? 'bg-blue-600' : 'bg-gray-200'
                }`}
              >
                <span
                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                    tempSettings.enableSounds ? 'translate-x-6' : 'translate-x-1'
                  }`}
                />
              </button>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        {hasChanges && (
          <div className="flex space-x-3 p-4 bg-yellow-50 rounded-lg border border-yellow-200">
            <button
              onClick={saveSettings}
              className="btn-primary"
            >
              💾 Save Changes
            </button>
            <button
              onClick={cancelChanges}
              className="btn-secondary"
            >
              ❌ Cancel
            </button>
          </div>
        )}

        <div className="pt-4 border-t border-gray-200">
          <button
            onClick={resetSettings}
            className="btn-secondary text-sm"
          >
            🔄 Reset to Defaults
          </button>
        </div>
      </div>

      {/* Pomodoro Technique Info */}
      <div className="card bg-green-50 border-green-200">
        <h3 className="text-lg font-semibold text-green-900 mb-3">🍅 About the Pomodoro Technique</h3>
        <div className="space-y-2 text-green-800 text-sm">
          <p>The Pomodoro Technique is a time management method that uses a timer to break work into intervals:</p>
          <ul className="list-disc list-inside space-y-1 ml-4">
            <li>Work for 25 minutes (one "pomodoro")</li>
            <li>Take a 5-minute break</li>
            <li>After 4 pomodoros, take a longer 15-30 minute break</li>
            <li>Repeat the cycle</li>
          </ul>
          <p className="mt-3">This technique helps maintain focus and prevents burnout by ensuring regular breaks.</p>
        </div>
      </div>

      {/* Data Management */}
      <div className="card">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">💾 Data Management</h3>
        <div className="space-y-4">
          <div>
            <h4 className="font-medium text-gray-700 mb-2">Export Data</h4>
            <p className="text-sm text-gray-600 mb-3">Download your focus statistics and settings</p>
            <button
              onClick={() => {
                const data = {
                  settings: tempSettings,
                  stats: JSON.parse(localStorage.getItem('focuszone-stats') || '{}'),
                  blocklist: JSON.parse(localStorage.getItem('focuszone-blocklist') || '[]'),
                  exportDate: new Date().toISOString()
                }
                const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
                const url = URL.createObjectURL(blob)
                const a = document.createElement('a')
                a.href = url
                a.download = `focuszone-data-${new Date().toISOString().split('T')[0]}.json`
                a.click()
                URL.revokeObjectURL(url)
              }}
              className="btn-secondary text-sm"
            >
              📥 Export Data
            </button>
          </div>

          <div>
            <h4 className="font-medium text-gray-700 mb-2">Clear All Data</h4>
            <p className="text-sm text-gray-600 mb-3">Reset all statistics, settings, and blocklist</p>
            <button
              onClick={() => {
                if (confirm('Are you sure you want to clear all data? This cannot be undone.')) {
                  localStorage.removeItem('focuszone-stats')
                  localStorage.removeItem('focuszone-blocklist')
                  localStorage.removeItem('focuszone-settings')
                  window.location.reload()
                }
              }}
              className="btn-danger text-sm"
            >
              🗑️ Clear All Data
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Settings
