import { useState, useEffect } from 'react'
import Timer from './components/Timer'
import BlocklistManager from './components/BlocklistManager'
import SessionControls from './components/SessionControls'
import Statistics from './components/Statistics'
import Settings from './components/Settings'

function App() {
  const [activeTab, setActiveTab] = useState('timer')
  const [isSessionActive, setIsSessionActive] = useState(false)
  const [sessionType, setSessionType] = useState('work') // 'work' or 'break'
  const [blockedSites, setBlockedSites] = useState([])
  const [settings, setSettings] = useState({
    workDuration: 25,
    shortBreakDuration: 5,
    longBreakDuration: 15,
    sessionsUntilLongBreak: 4,
    enableNotifications: true,
    enableSounds: true
  })

  // Load data from localStorage on mount
  useEffect(() => {
    const savedBlockedSites = localStorage.getItem('focuszone-blocklist')
    const savedSettings = localStorage.getItem('focuszone-settings')

    if (savedBlockedSites) {
      setBlockedSites(JSON.parse(savedBlockedSites))
    }

    if (savedSettings) {
      setSettings(JSON.parse(savedSettings))
    }
  }, [])

  // Save blocklist to localStorage
  useEffect(() => {
    localStorage.setItem('focuszone-blocklist', JSON.stringify(blockedSites))
  }, [blockedSites])

  // Save settings to localStorage
  useEffect(() => {
    localStorage.setItem('focuszone-settings', JSON.stringify(settings))
  }, [settings])

  const tabs = [
    { id: 'timer', label: 'Timer', icon: '⏱️' },
    { id: 'blocklist', label: 'Blocklist', icon: '🚫' },
    { id: 'stats', label: 'Statistics', icon: '📊' },
    { id: 'settings', label: 'Settings', icon: '⚙️' }
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-4xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-lg">F</span>
              </div>
              <h1 className="text-2xl font-bold text-gray-900">FocusZone</h1>
            </div>

            {/* Session Status */}
            {isSessionActive && (
              <div className="flex items-center space-x-2">
                <div className={`w-3 h-3 rounded-full ${sessionType === 'work' ? 'bg-red-500' : 'bg-green-500'} animate-pulse`}></div>
                <span className="text-sm font-medium text-gray-700">
                  {sessionType === 'work' ? 'Focus Session Active' : 'Break Time'}
                </span>
              </div>
            )}
          </div>
        </div>
      </header>

      {/* Navigation Tabs */}
      <nav className="bg-white border-b border-gray-200">
        <div className="max-w-4xl mx-auto px-4">
          <div className="flex space-x-8">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-4 px-2 border-b-2 font-medium text-sm transition-colors duration-200 ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <span className="mr-2">{tab.icon}</span>
                {tab.label}
              </button>
            ))}
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <main className="max-w-4xl mx-auto px-4 py-8">
        {activeTab === 'timer' && (
          <div className="space-y-6">
            <Timer
              settings={settings}
              isSessionActive={isSessionActive}
              setIsSessionActive={setIsSessionActive}
              sessionType={sessionType}
              setSessionType={setSessionType}
              blockedSites={blockedSites}
            />
            <SessionControls
              isSessionActive={isSessionActive}
              setIsSessionActive={setIsSessionActive}
              sessionType={sessionType}
              blockedSites={blockedSites}
            />
          </div>
        )}

        {activeTab === 'blocklist' && (
          <BlocklistManager
            blockedSites={blockedSites}
            setBlockedSites={setBlockedSites}
          />
        )}

        {activeTab === 'stats' && (
          <Statistics />
        )}

        {activeTab === 'settings' && (
          <Settings
            settings={settings}
            setSettings={setSettings}
          />
        )}
      </main>
    </div>
  )
}

export default App
