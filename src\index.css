/* Reset and base styles */
* {
	margin: 0;
	padding: 0;
	box-sizing: border-box;
}

body {
	font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
	background-color: #f9fafb;
	color: #111827;
	line-height: 1.6;
}

/* Utility classes */
.min-h-screen {
	min-height: 100vh;
}

.max-w-4xl {
	max-width: 56rem;
}

.max-w-2xl {
	max-width: 42rem;
}

.max-w-md {
	max-width: 28rem;
}

.mx-auto {
	margin-left: auto;
	margin-right: auto;
}

.px-4 {
	padding-left: 1rem;
	padding-right: 1rem;
}

.py-4 {
	padding-top: 1rem;
	padding-bottom: 1rem;
}

.py-8 {
	padding-top: 2rem;
	padding-bottom: 2rem;
}

.p-6 {
	padding: 1.5rem;
}

.p-4 {
	padding: 1rem;
}

.p-3 {
	padding: 0.75rem;
}

.p-2 {
	padding: 0.5rem;
}

.mb-6 {
	margin-bottom: 1.5rem;
}

.mb-4 {
	margin-bottom: 1rem;
}

.mb-3 {
	margin-bottom: 0.75rem;
}

.mb-2 {
	margin-bottom: 0.5rem;
}

.mt-3 {
	margin-top: 0.75rem;
}

.space-y-6>*+* {
	margin-top: 1.5rem;
}

.space-y-4>*+* {
	margin-top: 1rem;
}

.space-y-3>*+* {
	margin-top: 0.75rem;
}

.space-y-2>*+* {
	margin-top: 0.5rem;
}

.space-x-3>*+* {
	margin-left: 0.75rem;
}

.space-x-2>*+* {
	margin-left: 0.5rem;
}

.space-x-8>*+* {
	margin-left: 2rem;
}

/* Flexbox */
.flex {
	display: flex;
}

.flex-1 {
	flex: 1 1 0%;
}

.items-center {
	align-items: center;
}

.justify-center {
	justify-content: center;
}

.justify-between {
	justify-content: space-between;
}

/* Grid */
.grid {
	display: grid;
}

.grid-cols-2 {
	grid-template-columns: repeat(2, minmax(0, 1fr));
}

.grid-cols-3 {
	grid-template-columns: repeat(3, minmax(0, 1fr));
}

.gap-4 {
	gap: 1rem;
}

.gap-2 {
	gap: 0.5rem;
}

/* Text */
.text-2xl {
	font-size: 1.5rem;
	line-height: 2rem;
}

.text-lg {
	font-size: 1.125rem;
	line-height: 1.75rem;
}

.text-sm {
	font-size: 0.875rem;
	line-height: 1.25rem;
}

.text-xs {
	font-size: 0.75rem;
	line-height: 1rem;
}

.text-4xl {
	font-size: 2.25rem;
	line-height: 2.5rem;
}

.text-3xl {
	font-size: 1.875rem;
	line-height: 2.25rem;
}

.font-bold {
	font-weight: 700;
}

.font-semibold {
	font-weight: 600;
}

.font-medium {
	font-weight: 500;
}

.font-mono {
	font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
}

.text-center {
	text-align: center;
}

/* Colors */
.bg-white {
	background-color: #ffffff;
}

.bg-gray-50 {
	background-color: #f9fafb;
}

.bg-gray-100 {
	background-color: #f3f4f6;
}

.bg-gray-200 {
	background-color: #e5e7eb;
}

.bg-blue-600 {
	background-color: #2563eb;
}

.bg-blue-50 {
	background-color: #eff6ff;
}

.bg-red-50 {
	background-color: #fef2f2;
}

.bg-green-50 {
	background-color: #f0fdf4;
}

.bg-yellow-50 {
	background-color: #fefce8;
}

.text-gray-900 {
	color: #111827;
}

.text-gray-700 {
	color: #374151;
}

.text-gray-600 {
	color: #4b5563;
}

.text-gray-500 {
	color: #6b7280;
}

.text-white {
	color: #ffffff;
}

.text-blue-600 {
	color: #2563eb;
}

.text-blue-800 {
	color: #1e40af;
}

.text-blue-900 {
	color: #1e3a8a;
}

.text-red-600 {
	color: #dc2626;
}

.text-red-700 {
	color: #b91c1c;
}

.text-green-600 {
	color: #16a34a;
}

.text-green-800 {
	color: #166534;
}

.text-yellow-600 {
	color: #ca8a04;
}

.text-yellow-800 {
	color: #92400e;
}

/* Borders */
.border {
	border-width: 1px;
}

.border-b {
	border-bottom-width: 1px;
}

.border-b-2 {
	border-bottom-width: 2px;
}

.border-l-4 {
	border-left-width: 4px;
}

.border-gray-200 {
	border-color: #e5e7eb;
}

.border-gray-300 {
	border-color: #d1d5db;
}

.border-blue-200 {
	border-color: #bfdbfe;
}

.border-blue-500 {
	border-color: #3b82f6;
}

.border-red-200 {
	border-color: #fecaca;
}

.border-green-200 {
	border-color: #bbf7d0;
}

.border-yellow-200 {
	border-color: #fef3c7;
}

.border-transparent {
	border-color: transparent;
}

/* Border radius */
.rounded-lg {
	border-radius: 0.5rem;
}

.rounded-xl {
	border-radius: 0.75rem;
}

.rounded-full {
	border-radius: 9999px;
}

/* Shadows */
.shadow-sm {
	box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
}

/* Width/Height */
.w-full {
	width: 100%;
}

.w-32 {
	width: 8rem;
}

.w-8 {
	width: 2rem;
}

.w-3 {
	width: 0.75rem;
}

.w-48 {
	width: 12rem;
}

.h-32 {
	height: 8rem;
}

.h-8 {
	height: 2rem;
}

.h-3 {
	height: 0.75rem;
}

.h-48 {
	height: 12rem;
}

.h-full {
	height: 100%;
}

/* Animations */
.animate-pulse {
	animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {

	0%,
	100% {
		opacity: 1;
	}

	50% {
		opacity: .5;
	}
}

/* Transitions */
.transition-colors {
	transition-property: color, background-color, border-color;
	transition-duration: 200ms;
}

.duration-200 {
	transition-duration: 200ms;
}

/* Component styles */
.btn-primary {
	background-color: #2563eb;
	color: white;
	font-weight: 500;
	padding: 0.5rem 1rem;
	border-radius: 0.5rem;
	border: none;
	cursor: pointer;
	transition: background-color 200ms;
}

.btn-primary:hover {
	background-color: #1d4ed8;
}

.btn-secondary {
	background-color: #e5e7eb;
	color: #374151;
	font-weight: 500;
	padding: 0.5rem 1rem;
	border-radius: 0.5rem;
	border: none;
	cursor: pointer;
	transition: background-color 200ms;
}

.btn-secondary:hover {
	background-color: #d1d5db;
}

.btn-danger {
	background-color: #dc2626;
	color: white;
	font-weight: 500;
	padding: 0.5rem 1rem;
	border-radius: 0.5rem;
	border: none;
	cursor: pointer;
	transition: background-color 200ms;
}

.btn-danger:hover {
	background-color: #b91c1c;
}

.card {
	background-color: white;
	border-radius: 0.75rem;
	box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
	border: 1px solid #e5e7eb;
	padding: 1.5rem;
}

.input-field {
	width: 100%;
	padding: 0.5rem 0.75rem;
	border: 1px solid #d1d5db;
	border-radius: 0.5rem;
	outline: none;
}

.input-field:focus {
	border-color: #3b82f6;
	box-shadow: 0 0 0 2px rgb(59 130 246 / 0.5);
}

/* Responsive */
@media (min-width: 768px) {
	.md\\:grid-cols-3 {
		grid-template-columns: repeat(3, minmax(0, 1fr));
	}

	.md\\:grid-cols-2 {
		grid-template-columns: repeat(2, minmax(0, 1fr));
	}
}