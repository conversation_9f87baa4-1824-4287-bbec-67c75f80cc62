import { useState } from 'react'

function BlocklistManager({ blockedSites, setBlockedSites }) {
  const [newSite, setNewSite] = useState('')
  const [isAdding, setIsAdding] = useState(false)

  const addSite = () => {
    if (!newSite.trim()) return
    
    // Clean up the URL
    let cleanUrl = newSite.trim().toLowerCase()
    cleanUrl = cleanUrl.replace(/^https?:\/\//, '')
    cleanUrl = cleanUrl.replace(/^www\./, '')
    cleanUrl = cleanUrl.replace(/\/$/, '')
    
    if (cleanUrl && !blockedSites.includes(cleanUrl)) {
      setBlockedSites([...blockedSites, cleanUrl])
      setNewSite('')
      setIsAdding(false)
    }
  }

  const removeSite = (siteToRemove) => {
    setBlockedSites(blockedSites.filter(site => site !== siteToRemove))
  }

  const handleKeyPress = (e) => {
    if (e.key === 'Enter') {
      addSite()
    }
  }

  const commonSites = [
    'facebook.com',
    'twitter.com',
    'instagram.com',
    'youtube.com',
    'reddit.com',
    'tiktok.com',
    'linkedin.com',
    'pinterest.com',
    'snapchat.com',
    'discord.com'
  ]

  const addCommonSite = (site) => {
    if (!blockedSites.includes(site)) {
      setBlockedSites([...blockedSites, site])
    }
  }

  return (
    <div className="max-w-2xl mx-auto space-y-6">
      <div className="card">
        <h2 className="text-2xl font-bold text-gray-900 mb-4">🚫 Website Blocklist</h2>
        <p className="text-gray-600 mb-6">
          Add websites that distract you during focus sessions. These sites will be blocked when a timer is active.
        </p>

        {/* Add New Site */}
        <div className="mb-6">
          {!isAdding ? (
            <button
              onClick={() => setIsAdding(true)}
              className="btn-primary w-full"
            >
              ➕ Add Website
            </button>
          ) : (
            <div className="flex space-x-2">
              <input
                type="text"
                value={newSite}
                onChange={(e) => setNewSite(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="Enter website (e.g., facebook.com)"
                className="input-field flex-1"
                autoFocus
              />
              <button
                onClick={addSite}
                className="btn-primary px-4"
              >
                Add
              </button>
              <button
                onClick={() => {
                  setIsAdding(false)
                  setNewSite('')
                }}
                className="btn-secondary px-4"
              >
                Cancel
              </button>
            </div>
          )}
        </div>

        {/* Current Blocked Sites */}
        {blockedSites.length > 0 && (
          <div className="mb-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-3">Blocked Websites</h3>
            <div className="space-y-2">
              {blockedSites.map((site, index) => (
                <div
                  key={index}
                  className="flex items-center justify-between p-3 bg-gray-50 rounded-lg border"
                >
                  <div className="flex items-center space-x-3">
                    <span className="text-red-500">🚫</span>
                    <span className="font-medium text-gray-900">{site}</span>
                  </div>
                  <button
                    onClick={() => removeSite(site)}
                    className="text-red-600 hover:text-red-800 p-1"
                    title="Remove from blocklist"
                  >
                    ❌
                  </button>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Common Sites */}
        <div>
          <h3 className="text-lg font-semibold text-gray-900 mb-3">Quick Add - Common Distracting Sites</h3>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
            {commonSites.map((site) => (
              <button
                key={site}
                onClick={() => addCommonSite(site)}
                disabled={blockedSites.includes(site)}
                className={`p-2 text-sm rounded-lg border transition-colors ${
                  blockedSites.includes(site)
                    ? 'bg-gray-100 text-gray-400 border-gray-200 cursor-not-allowed'
                    : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                }`}
              >
                {blockedSites.includes(site) ? '✅' : '➕'} {site}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* How It Works */}
      <div className="card bg-blue-50 border-blue-200">
        <h3 className="text-lg font-semibold text-blue-900 mb-3">💡 How Website Blocking Works</h3>
        <div className="text-blue-800 space-y-2 text-sm">
          <p>• Websites are blocked automatically when you start a focus session</p>
          <p>• Blocking is lifted during break periods</p>
          <p>• For full blocking functionality, consider installing a browser extension</p>
          <p>• This app provides visual reminders and can integrate with system-level blockers</p>
        </div>
      </div>

      {/* Browser Extension Recommendation */}
      <div className="card bg-yellow-50 border-yellow-200">
        <h3 className="text-lg font-semibold text-yellow-900 mb-3">🔧 Enhanced Blocking</h3>
        <p className="text-yellow-800 text-sm mb-3">
          For stronger website blocking, we recommend installing a browser extension like:
        </p>
        <div className="space-y-2 text-sm">
          <div className="flex items-center space-x-2">
            <span className="text-yellow-600">•</span>
            <span className="text-yellow-800">BlockSite (Chrome/Firefox)</span>
          </div>
          <div className="flex items-center space-x-2">
            <span className="text-yellow-600">•</span>
            <span className="text-yellow-800">Cold Turkey Blocker (Desktop)</span>
          </div>
          <div className="flex items-center space-x-2">
            <span className="text-yellow-600">•</span>
            <span className="text-yellow-800">Focus (macOS/iOS)</span>
          </div>
        </div>
      </div>
    </div>
  )
}

export default BlocklistManager
