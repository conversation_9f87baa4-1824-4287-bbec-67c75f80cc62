import { useState, useEffect } from 'react'

function Statistics() {
  const [stats, setStats] = useState({})
  const [selectedPeriod, setSelectedPeriod] = useState('week') // 'week', 'month', 'all'

  useEffect(() => {
    const savedStats = localStorage.getItem('focuszone-stats')
    if (savedStats) {
      setStats(JSON.parse(savedStats))
    }
  }, [])

  const getDateRange = (period) => {
    const today = new Date()
    const dates = []
    
    if (period === 'week') {
      for (let i = 6; i >= 0; i--) {
        const date = new Date(today)
        date.setDate(today.getDate() - i)
        dates.push(date.toDateString())
      }
    } else if (period === 'month') {
      for (let i = 29; i >= 0; i--) {
        const date = new Date(today)
        date.setDate(today.getDate() - i)
        dates.push(date.toDateString())
      }
    } else {
      // All time - get all dates from stats
      return Object.keys(stats).sort((a, b) => new Date(a) - new Date(b))
    }
    
    return dates
  }

  const getStatsForPeriod = (period) => {
    const dates = getDateRange(period)
    const periodStats = dates.map(date => ({
      date,
      sessions: stats[date] || 0
    }))
    
    const totalSessions = periodStats.reduce((sum, day) => sum + day.sessions, 0)
    const avgSessions = totalSessions / dates.length
    const maxSessions = Math.max(...periodStats.map(day => day.sessions))
    const streakDays = calculateStreak()
    
    return {
      periodStats,
      totalSessions,
      avgSessions: Math.round(avgSessions * 10) / 10,
      maxSessions,
      streakDays
    }
  }

  const calculateStreak = () => {
    const today = new Date()
    let streak = 0
    
    for (let i = 0; i < 365; i++) {
      const date = new Date(today)
      date.setDate(today.getDate() - i)
      const dateString = date.toDateString()
      
      if (stats[dateString] && stats[dateString] > 0) {
        streak++
      } else {
        break
      }
    }
    
    return streak
  }

  const formatDate = (dateString) => {
    const date = new Date(dateString)
    return date.toLocaleDateString([], { month: 'short', day: 'numeric' })
  }

  const getDayName = (dateString) => {
    const date = new Date(dateString)
    return date.toLocaleDateString([], { weekday: 'short' })
  }

  const currentStats = getStatsForPeriod(selectedPeriod)

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <div className="card">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-bold text-gray-900">📊 Your Focus Statistics</h2>
          <div className="flex space-x-2">
            {['week', 'month', 'all'].map((period) => (
              <button
                key={period}
                onClick={() => setSelectedPeriod(period)}
                className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                  selectedPeriod === period
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                {period === 'week' ? 'Last 7 Days' : period === 'month' ? 'Last 30 Days' : 'All Time'}
              </button>
            ))}
          </div>
        </div>

        {/* Key Metrics */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
          <div className="text-center p-4 bg-blue-50 rounded-lg">
            <div className="text-2xl font-bold text-blue-600">{currentStats.totalSessions}</div>
            <div className="text-sm text-blue-800">Total Sessions</div>
          </div>
          <div className="text-center p-4 bg-green-50 rounded-lg">
            <div className="text-2xl font-bold text-green-600">{currentStats.avgSessions}</div>
            <div className="text-sm text-green-800">Avg per Day</div>
          </div>
          <div className="text-center p-4 bg-purple-50 rounded-lg">
            <div className="text-2xl font-bold text-purple-600">{currentStats.maxSessions}</div>
            <div className="text-sm text-purple-800">Best Day</div>
          </div>
          <div className="text-center p-4 bg-orange-50 rounded-lg">
            <div className="text-2xl font-bold text-orange-600">{currentStats.streakDays}</div>
            <div className="text-sm text-orange-800">Day Streak</div>
          </div>
        </div>

        {/* Chart */}
        <div className="mb-4">
          <h3 className="text-lg font-semibold text-gray-900 mb-3">Daily Sessions</h3>
          <div className="flex items-end space-x-1 h-32">
            {currentStats.periodStats.map((day, index) => {
              const height = currentStats.maxSessions > 0 
                ? (day.sessions / currentStats.maxSessions) * 100 
                : 0
              
              return (
                <div key={index} className="flex-1 flex flex-col items-center">
                  <div className="w-full flex justify-center mb-2">
                    <div
                      className="bg-blue-500 rounded-t min-h-[4px] w-full max-w-8 transition-all duration-300"
                      style={{ height: `${Math.max(height, 4)}%` }}
                      title={`${day.sessions} sessions on ${formatDate(day.date)}`}
                    ></div>
                  </div>
                  <div className="text-xs text-gray-500 text-center">
                    {selectedPeriod === 'week' ? getDayName(day.date) : formatDate(day.date)}
                  </div>
                  <div className="text-xs font-medium text-gray-700">
                    {day.sessions}
                  </div>
                </div>
              )
            })}
          </div>
        </div>
      </div>

      {/* Achievements */}
      <div className="card">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">🏆 Achievements</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className={`p-4 rounded-lg border-2 ${
            currentStats.totalSessions >= 1 ? 'bg-yellow-50 border-yellow-300' : 'bg-gray-50 border-gray-200'
          }`}>
            <div className="flex items-center space-x-3">
              <span className="text-2xl">{currentStats.totalSessions >= 1 ? '🥉' : '⭕'}</span>
              <div>
                <div className="font-medium">First Session</div>
                <div className="text-sm text-gray-600">Complete your first focus session</div>
              </div>
            </div>
          </div>
          
          <div className={`p-4 rounded-lg border-2 ${
            currentStats.totalSessions >= 10 ? 'bg-yellow-50 border-yellow-300' : 'bg-gray-50 border-gray-200'
          }`}>
            <div className="flex items-center space-x-3">
              <span className="text-2xl">{currentStats.totalSessions >= 10 ? '🥈' : '⭕'}</span>
              <div>
                <div className="font-medium">Getting Started</div>
                <div className="text-sm text-gray-600">Complete 10 focus sessions</div>
              </div>
            </div>
          </div>
          
          <div className={`p-4 rounded-lg border-2 ${
            currentStats.totalSessions >= 50 ? 'bg-yellow-50 border-yellow-300' : 'bg-gray-50 border-gray-200'
          }`}>
            <div className="flex items-center space-x-3">
              <span className="text-2xl">{currentStats.totalSessions >= 50 ? '🥇' : '⭕'}</span>
              <div>
                <div className="font-medium">Focus Master</div>
                <div className="text-sm text-gray-600">Complete 50 focus sessions</div>
              </div>
            </div>
          </div>
          
          <div className={`p-4 rounded-lg border-2 ${
            currentStats.streakDays >= 7 ? 'bg-yellow-50 border-yellow-300' : 'bg-gray-50 border-gray-200'
          }`}>
            <div className="flex items-center space-x-3">
              <span className="text-2xl">{currentStats.streakDays >= 7 ? '🔥' : '⭕'}</span>
              <div>
                <div className="font-medium">Week Warrior</div>
                <div className="text-sm text-gray-600">7-day focus streak</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Tips */}
      <div className="card bg-blue-50 border-blue-200">
        <h3 className="text-lg font-semibold text-blue-900 mb-3">💡 Improve Your Focus</h3>
        <div className="space-y-2 text-blue-800 text-sm">
          <p>• Aim for at least 2-3 focus sessions per day</p>
          <p>• Consistency is key - try to focus every day, even if just for one session</p>
          <p>• Track your most productive times and schedule important work then</p>
          <p>• Celebrate your achievements and progress!</p>
        </div>
      </div>
    </div>
  )
}

export default Statistics
